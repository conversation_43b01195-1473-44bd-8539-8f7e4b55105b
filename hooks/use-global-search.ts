"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { useRouter } from "next/navigation"
import { searchAllEntities } from "@/lib/actions"
import { debounce } from "lodash"

export type SearchResult = {
  id: string
  title: string
  subtitle?: string
  href: string
  type: "employee" | "department" | "manager" | "period" | "navigation" | "pto"
  icon: string
}

export function useGlobalSearch() {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([])
  const router = useRouter()

  // Debounced search function (only for actual searches)
  const debouncedSearch = useMemo(
    () => debounce(async (searchQuery: string) => {
      console.log('🔍 [FRONTEND DEBUG] Debounced search called with:', searchQuery)
      console.log('🔍 [FRONTEND DEBUG] Starting search for:', searchQuery)
      setLoading(true)
      try {
        console.log('🔍 [FRONTEND DEBUG] About to call searchAllEntities...')
        const searchResults = await searchAllEntities(searchQuery)
        console.log('🔍 [FRONTEND DEBUG] Search results received:', searchResults)
        console.log('🔍 [FRONTEND DEBUG] Search results type:', typeof searchResults)
        console.log('🔍 [FRONTEND DEBUG] Search results length:', searchResults?.length)
        setResults(searchResults)
        console.log('🔍 [FRONTEND DEBUG] Results set in state')
      } catch (error) {
        console.error("🔍 [FRONTEND DEBUG] Search failed:", error)
        setResults([])
      } finally {
        setLoading(false)
        console.log('🔍 [FRONTEND DEBUG] Loading set to false')
      }
    }, 300),
    [] // 🔧 FIX: No dependencies to prevent debounce cancellation
  )

  // Handle search query changes
  useEffect(() => {
    if (!query.trim()) {
      console.log('🔍 [FRONTEND DEBUG] Empty query, showing recent searches')
      setResults(recentSearches.slice(0, 5))
      setLoading(false)
      debouncedSearch.cancel() // Cancel any pending searches
      return
    }
    
    debouncedSearch(query)
    return () => debouncedSearch.cancel()
  }, [query, debouncedSearch, recentSearches])

  // Load recent searches from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem("global-search-recent")
      if (stored) {
        setRecentSearches(JSON.parse(stored))
      }
    } catch (error) {
      console.error("Failed to load recent searches:", error)
    }
  }, [])

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((result: SearchResult) => {
    setRecentSearches(prev => {
      const filtered = prev.filter(item => item.id !== result.id)
      const updated = [result, ...filtered].slice(0, 10) // Keep last 10
      
      try {
        localStorage.setItem("global-search-recent", JSON.stringify(updated))
      } catch (error) {
        console.error("Failed to save recent search:", error)
      }
      
      return updated
    })
  }, [])

  // Handle result selection
  const selectResult = useCallback((result: SearchResult) => {
    console.log('🎯 [NAVIGATION DEBUG] Selecting result:', result)
    console.log('🎯 [NAVIGATION DEBUG] Navigating to:', result.href)
    console.log('🎯 [NAVIGATION DEBUG] Result type:', result.type)

    saveRecentSearch(result)
    setIsOpen(false)
    setQuery("")
    router.push(result.href)
  }, [router, saveRecentSearch])

  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "k" && (event.metaKey || event.ctrlKey)) {
        event.preventDefault()
        setIsOpen(prev => !prev)
      }
      
      if (event.key === "Escape" && isOpen) {
        event.preventDefault()
        setIsOpen(false)
        setQuery("")
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [isOpen])

  // Reset when closing
  useEffect(() => {
    if (!isOpen) {
      setQuery("")
      setResults(recentSearches.slice(0, 5))
    }
  }, [isOpen, recentSearches])

  // 🔧 FIX: Simplify results logic to prevent display issues
  const displayResults = query.trim() ? results : recentSearches.slice(0, 5)
  
  console.log('🔍 [FRONTEND DEBUG] Display logic:', {
    hasQuery: !!query.trim(),
    queryLength: query.length,
    resultsLength: results.length,
    recentLength: recentSearches.length,
    displayLength: displayResults.length,
    loading
  })

  return {
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results: displayResults,
    loading,
    selectResult,
    showingRecent: !query.trim(),
  }
}