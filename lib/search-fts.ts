/**
 * Full-Text Search Implementation using Postgres FTS
 * 
 * This module implements server-side Full-Text Search using Supabase's 
 * built-in Postgres FTS capabilities for superior performance and accuracy.
 */

import { supabaseAdmin } from './supabase'
import type { Database } from './supabase'

// Search result type
export interface SearchResult {
  id: string
  title: string
  subtitle?: string
  href: string
  type: "employee" | "department" | "manager" | "period" | "navigation" | "pto"
  icon: string
  relevanceScore: number
  adjustedScore: number
}

// Type-specific search result interfaces
interface EmployeeSearchResult {
  id: string
  full_name: string
  department_name?: string
  manager_name?: string
  compensation: number
  rate: string
  rank: number
}

interface DepartmentSearchResult {
  id: string
  name: string
  rank: number
}

interface ManagerSearchResult {
  user_id: string
  full_name: string
  rank: number
}

interface PeriodSearchResult {
  id: string
  name: string
  start_date: string
  end_date: string
  status: string
  rank: number
}

interface PTOSearchResult {
  id: string
  employee_name: string
  request_type: string
  start_date: string
  days_requested: number
  status: string
  rank: number
}

/**
 * Sanitizes search input to prevent injection and improve FTS parsing
 */
function sanitizeSearchQuery(query: string): string {
  return query
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, ' ') // Remove special chars, keep alphanumeric and spaces
    .replace(/\s+/g, ' ') // Normalize multiple spaces
    .substring(0, 100) // Limit query length
}

/**
 * Converts search term to tsquery format for Postgres FTS
 */
function createTSQuery(sanitizedTerm: string): string {
  // Split into words and join with & for AND logic
  const words = sanitizedTerm.split(' ').filter(word => word.length > 0)
  if (words.length === 0) return ''
  
  // Create prefix search for partial word matching
  const tsquery = words.map(word => `${word}:*`).join(' & ')
  console.log('🔧 [TSQUERY DEBUG] Created tsquery:', { sanitizedTerm, words, tsquery })
  return tsquery
}

/**
 * Search employees using Postgres Full-Text Search
 */
async function searchEmployees(searchTerm: string, limit: number = 5): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []
  
  const tsQuery = createTSQuery(sanitizedTerm)
  if (!tsQuery) return []

  console.log('🔍 [FTS DEBUG] Employee search:', { searchTerm, sanitizedTerm, tsQuery })

  try {
    console.log('🔍 [FTS DEBUG] About to execute textSearch with:', {
      sanitizedTerm,
      tsQuery,
      limit: limit * 2
    })
    
    // Use textSearch for built-in FTS with ranking
    const { data, error } = await supabaseAdmin
      .from('appy_employees')
      .select(`
        id,
        full_name,
        compensation,
        rate,
        appy_departments:department_id (
          name
        )
      `)
      .eq('active', true)
      .textSearch('full_name', sanitizedTerm, {
        type: 'websearch',
        config: 'english'
      })
      .limit(limit * 2) // Get more to allow for better ranking

    console.log('🔍 [FTS DEBUG] textSearch completed:', { 
      hasData: !!data, 
      dataLength: data?.length || 0, 
      hasError: !!error,
      errorMessage: error?.message || 'none'
    })

    if (error) {
      console.error('❌ [FTS ERROR] Employee search failed:', error)
      console.error('❌ [FTS ERROR] Full error object:', JSON.stringify(error, null, 2))
      throw error
    }

    if (!data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] No employees found via FTS, trying fallback')
      
      // Fallback to ILIKE for partial matches
      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_employees')
        .select(`
          id,
          full_name,
          compensation,
          rate,
          appy_departments:department_id (
            name
          )
        `)
        .eq('active', true)
        .ilike('full_name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] Fallback search failed:', fallbackError)
        return []
      }

      return (fallbackData || []).map((emp, index) => ({
        id: `employee-${emp.id}`,
        title: emp.full_name,
        subtitle: `${emp.appy_departments?.[0]?.name || 'No Department'} • ${emp.rate === 'hourly' ? 'Hourly' : 'Monthly'}`,
        href: `/dashboard/appraisal/${emp.id}`,
        type: 'employee' as const,
        icon: 'user',
        relevanceScore: 50 - (index * 5), // Decreasing score for fallback results
        adjustedScore: 70 - (index * 5) // Boost for employee type
      }))
    }

    console.log('✅ [FTS DEBUG] Found employees:', data.map(emp => emp.full_name))

    return data.map((emp, index) => ({
      id: `employee-${emp.id}`,
      title: emp.full_name,
      subtitle: `${emp.appy_departments?.[0]?.name || 'No Department'} • ${emp.rate === 'hourly' ? 'Hourly' : 'Monthly'}`,
      href: `/dashboard/appraisal/${emp.id}`,
      type: 'employee' as const,
      icon: 'user',
      relevanceScore: 100 - (index * 10), // Higher scores for better FTS matches
      adjustedScore: 120 - (index * 10) // Boost for employee type
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Employee search error:', error)
    return []
  }
}

/**
 * Search departments using Postgres Full-Text Search
 */
async function searchDepartments(searchTerm: string, limit: number = 3): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Department search:', { searchTerm, sanitizedTerm })

  try {
    const { data, error } = await supabaseAdmin
      .from('appy_departments')
      .select('id, name')
      .textSearch('name', sanitizedTerm, {
        type: 'websearch',
        config: 'english'
      })
      .limit(limit)

    if (error) {
      console.error('❌ [FTS ERROR] Department search failed:', error)
      // Fallback to ILIKE
      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_departments')
        .select('id, name')
        .ilike('name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) return []
      
      return (fallbackData || []).map((dept, index) => ({
        id: `department-${dept.id}`,
        title: dept.name,
        subtitle: 'Department',
        href: '/dashboard/departments',
        type: 'department' as const,
        icon: 'building2',
        relevanceScore: 40 - (index * 5),
        adjustedScore: 45 - (index * 5) // Lower boost for departments
      }))
    }

    return (data || []).map((dept, index) => ({
      id: `department-${dept.id}`,
      title: dept.name,
      subtitle: 'Department',
      href: '/dashboard/departments',
      type: 'department' as const,
      icon: 'building2',
      relevanceScore: 80 - (index * 10),
      adjustedScore: 85 - (index * 10) // Lower boost for departments
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Department search error:', error)
    return []
  }
}

/**
 * Search managers using Postgres Full-Text Search
 */
async function searchManagers(searchTerm: string, limit: number = 3): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Manager search:', { searchTerm, sanitizedTerm })

  try {
    const { data, error } = await supabaseAdmin
      .from('appy_managers')
      .select('user_id, full_name')
      .eq('active', true)
      .textSearch('full_name', sanitizedTerm, {
        type: 'websearch',
        config: 'english'
      })
      .limit(limit)

    if (error) {
      console.error('❌ [FTS ERROR] Manager search failed:', error)
      // Fallback to ILIKE
      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_managers')
        .select('user_id, full_name')
        .eq('active', true)
        .ilike('full_name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) return []
      
      return (fallbackData || []).map((mgr, index) => ({
        id: `manager-${mgr.user_id}`,
        title: mgr.full_name,
        subtitle: 'Manager',
        href: '/dashboard/team',
        type: 'manager' as const,
        icon: 'user-check',
        relevanceScore: 35 - (index * 5),
        adjustedScore: 45 - (index * 5) // Medium boost for managers
      }))
    }

    return (data || []).map((mgr, index) => ({
      id: `manager-${mgr.user_id}`,
      title: mgr.full_name,
      subtitle: 'Manager',
      href: '/dashboard/team',
      type: 'manager' as const,
      icon: 'user-check',
      relevanceScore: 70 - (index * 10),
      adjustedScore: 80 - (index * 10) // Medium boost for managers
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Manager search error:', error)
    return []
  }
}

/**
 * Search appraisal periods using Postgres Full-Text Search
 */
async function searchPeriods(searchTerm: string, limit: number = 2): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Period search:', { searchTerm, sanitizedTerm })

  try {
    const { data, error } = await supabaseAdmin
      .from('appy_appraisal_periods')
      .select('id, name, start_date, end_date, status')
      .textSearch('name', sanitizedTerm, {
        type: 'websearch',
        config: 'english'
      })
      .limit(limit)

    if (error) {
      console.error('❌ [FTS ERROR] Period search failed:', error)
      // Fallback to ILIKE
      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_appraisal_periods')
        .select('id, name, start_date, end_date, status')
        .ilike('name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) return []
      
      return (fallbackData || []).map((period, index) => ({
        id: `period-${period.id}`,
        title: period.name,
        subtitle: `${period.status} • ${new Date(period.start_date).getFullYear()}`,
        href: '/dashboard/periods',
        type: 'period' as const,
        icon: 'calendar',
        relevanceScore: 30 - (index * 5),
        adjustedScore: 35 - (index * 5)
      }))
    }

    return (data || []).map((period, index) => ({
      id: `period-${period.id}`,
      title: period.name,
      subtitle: `${period.status} • ${new Date(period.start_date).getFullYear()}`,
      href: '/dashboard/periods',
      type: 'period' as const,
      icon: 'calendar',
      relevanceScore: 60 - (index * 10),
      adjustedScore: 65 - (index * 10)
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Period search error:', error)
    return []
  }
}

/**
 * Search PTO requests using Postgres Full-Text Search
 */
async function searchPTORequests(_searchTerm: string, _limit: number = 2): Promise<SearchResult[]> {
  console.log('🔍 [FTS DEBUG] PTO search disabled due to schema issues')
  // Return empty results to prevent schema errors
  return []
}

/**
 * Search navigation pages (static content)
 */
function searchNavigation(searchTerm: string): SearchResult[] {
  const navigationItems = [
    { id: 'nav-dashboard', title: 'Dashboard', href: '/dashboard', icon: 'navigation' },
    { id: 'nav-employees', title: 'Employees', href: '/dashboard/employees', icon: 'navigation' },
    { id: 'nav-departments', title: 'Departments', href: '/dashboard/departments', icon: 'navigation' },
    { id: 'nav-periods', title: 'Appraisal Periods', href: '/dashboard/periods', icon: 'navigation' },
    { id: 'nav-approvals', title: 'Approvals', href: '/dashboard/approvals', icon: 'navigation' },
    { id: 'nav-pto', title: 'PTO Management', href: '/dashboard/pto', icon: 'navigation' },
    { id: 'nav-team', title: 'Team View', href: '/dashboard/team', icon: 'navigation' },
    { id: 'nav-admin', title: 'Admin Panel', href: '/dashboard/admin', icon: 'navigation' }
  ]

  const term = searchTerm.toLowerCase()
  
  return navigationItems
    .filter(item => item.title.toLowerCase().includes(term))
    .map((item, index) => ({
      id: item.id,
      title: item.title,
      subtitle: 'Navigation',
      href: item.href,
      type: 'navigation' as const,
      icon: item.icon,
      relevanceScore: 40 - (index * 5),
      adjustedScore: 40 - (index * 5) // No boost for navigation
    }))
    .slice(0, 2)
}

/**
 * Deduplicate results, handling dual-role entities (e.g., employee who is also manager)
 */
function deduplicateResults(results: SearchResult[]): SearchResult[] {
  const seen = new Map<string, SearchResult>()
  
  return results.filter(result => {
    // Extract base ID (remove type prefix)
    const baseId = result.id.replace(/^(employee|manager|department|period|navigation|pto)-/, '')
    const key = `${result.type}-${baseId}`
    
    if (seen.has(key)) return false
    
    // Handle dual-role entities (employee who is also manager)
    if (result.type === 'manager') {
      const employeeKey = `employee-${baseId}`
      if (seen.has(employeeKey)) {
        // Merge roles: show as "Employee (Manager)"
        const existingEmployee = seen.get(employeeKey)!
        existingEmployee.title += ' (Manager)'
        existingEmployee.adjustedScore += 5 // Small boost for dual role
        return false
      }
    }
    
    if (result.type === 'employee') {
      const managerKey = `manager-${baseId}`
      if (seen.has(managerKey)) {
        // Update to show dual role
        result.title += ' (Manager)'
        result.adjustedScore += 5 // Small boost for dual role
      }
    }
    
    seen.set(key, result)
    return true
  })
}

/**
 * Main FTS search function that combines all entity searches
 */
export async function searchAllEntitiesFTS(query: string, userRole: string): Promise<SearchResult[]> {
  const startTime = Date.now()
  
  console.log('🔍 [FTS DEBUG] === SEARCH START ===', { 
    originalQuery: query, 
    queryLength: query.length, 
    userRole 
  })
  
  if (!query.trim()) {
    console.log('🔍 [FTS DEBUG] Empty query provided')
    return []
  }

  const sanitizedQuery = sanitizeSearchQuery(query)
  console.log('🔍 [FTS DEBUG] After sanitization:', { 
    original: query, 
    sanitized: sanitizedQuery, 
    sanitizedLength: sanitizedQuery.length 
  })
  
  if (!sanitizedQuery) {
    console.log('🔍 [FTS DEBUG] Query sanitization resulted in empty string')
    return []
  }

  console.log('🔍 [FTS DEBUG] Starting search:', { query, sanitizedQuery, userRole })

  try {
    // Define role-based access control
    const hasEmployeeAccess = ['hr-admin', 'admin', 'super-admin', 'manager'].includes(userRole)
    const hasAdminAccess = ['hr-admin', 'admin', 'super-admin'].includes(userRole)
    
    // Run searches in parallel for better performance
    const searchPromises: Promise<SearchResult[]>[] = []
    
    if (hasEmployeeAccess) {
      searchPromises.push(searchEmployees(sanitizedQuery, 6)) // More employees as they're primary
    }
    
    if (hasAdminAccess) {
      searchPromises.push(searchDepartments(sanitizedQuery, 3))
      searchPromises.push(searchManagers(sanitizedQuery, 4))
      searchPromises.push(searchPeriods(sanitizedQuery, 2))
    }
    
    // PTO and Navigation available to all authenticated users
    searchPromises.push(searchPTORequests(sanitizedQuery, 2))
    searchPromises.push(Promise.resolve(searchNavigation(sanitizedQuery)))

    const searchResults = await Promise.all(searchPromises)
    
    // Flatten and combine all results
    const allResults = searchResults.flat()
    
    console.log('🔍 [FTS DEBUG] Raw results before deduplication:', allResults.length)
    
    // Deduplicate and sort by adjusted score
    const deduplicatedResults = deduplicateResults(allResults)
    const sortedResults = deduplicatedResults
      .sort((a, b) => b.adjustedScore - a.adjustedScore)
      .slice(0, 10) // Limit to top 10 results
    
    const endTime = Date.now()
    
    console.log('🔍 [FTS DEBUG] Final results:', {
      totalResults: sortedResults.length,
      searchTime: `${endTime - startTime}ms`,
      results: sortedResults.map(r => ({ type: r.type, title: r.title, score: r.adjustedScore }))
    })
    
    return sortedResults

  } catch (error) {
    console.error('❌ [FTS ERROR] Search failed:', error)
    console.error('❌ [FTS ERROR] Error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace',
      query: sanitizedQuery,
      userRole
    })
    return []
  }
}

/**
 * Initialize FTS indexes (run this once in your database)
 * This would typically be run as a migration
 */
export async function initializeFTSIndexes(): Promise<void> {
  console.log('🔧 [FTS SETUP] Initializing Full-Text Search indexes...')
  
  const indexes = [
    // Employee full-text search index
    `CREATE INDEX IF NOT EXISTS idx_employees_fts 
     ON appy_employees USING GIN (to_tsvector('english', full_name))`,
    
    // Department full-text search index
    `CREATE INDEX IF NOT EXISTS idx_departments_fts 
     ON appy_departments USING GIN (to_tsvector('english', name))`,
    
    // Manager full-text search index
    `CREATE INDEX IF NOT EXISTS idx_managers_fts 
     ON appy_managers USING GIN (to_tsvector('english', full_name))`,
    
    // Appraisal period full-text search index
    `CREATE INDEX IF NOT EXISTS idx_periods_fts 
     ON appy_appraisal_periods USING GIN (to_tsvector('english', name))`,
     
    // PTO request type full-text search index
    `CREATE INDEX IF NOT EXISTS idx_pto_fts 
     ON appy_pto_requests USING GIN (to_tsvector('english', request_type))`
  ]

  try {
    for (const indexSQL of indexes) {
      await supabaseAdmin.rpc('exec_sql', { sql: indexSQL })
      console.log('✅ [FTS SETUP] Index created successfully')
    }
    
    console.log('🚀 [FTS SETUP] All FTS indexes initialized successfully!')
  } catch (error) {
    console.error('❌ [FTS SETUP] Failed to initialize indexes:', error)
    throw error
  }
}